#ifndef ELECTROLYZER_CONTROLLER_H
#define ELECTROLYZER_CONTROLLER_H

/**
 * ElectrolyzerController.h
 * Controls the electrolyzer subsystem for hydrogen production
 * Manages water flow, temperature, pressure, and production state
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * ElectrolyzerController class
 * Manages the electrolyzer operation including water supply,
 * temperature control, and hydrogen/oxygen production
 */
class ElectrolyzerController {
public:
  /**
   * State structure containing all electrolyzer operational parameters
   * Used to track current conditions and control state
   */
  struct State {
    float elecTemp;       // Electrolyzer temperature (°C)
    float waterTemp;      // Water temperature (°C)
    float waterPressure;  // Water pressure (bar)
    float elecCurrent;    // Electrolyzer current (A)
    float elecVoltage;    // Electrolyzer voltage (V)
    float waterLevel;     // Water level (%)
    bool isHeaterOn;      // Heater state
    bool isWaterPumpOn;   // Water pump state
    bool isChillerOn;     // Chiller state
    bool isProducing;     // Production state
  };

  static constexpr float WATER_PRESSURE_START = 3.5f;
  static constexpr float WATER_PRESSURE_STOP  = 4.0f;
  static constexpr float WATER_LEVEL_MIN      = 2.0f;
  static constexpr float WATER_TEMP_LOW       = 20.0f;
  static constexpr float WATER_TEMP_TARGET    = 80.0f;
  static constexpr float WATER_TEMP_HIGH      = 85.0f;
  static constexpr float ELEC_TEMP_HIGH       = 85.0f;
  static constexpr float PRODUCTION_START_TEMP = 65.0f;
  static constexpr float PRODUCTION_STOP_TEMP  = 63.0f;

private:
  State elecState;
  // Debounce interval for relay switching (5 seconds)
  static const unsigned long ELECTROLYZER_MIN_SWITCH_INTERVAL = 5000UL;
  unsigned long lastPumpSwitchTime;
  unsigned long lastHeaterSwitchTime;
  unsigned long lastChillerSwitchTime;
  unsigned long lastProductionSwitchTime;
  bool emergencyActive;

public:
  ElectrolyzerController();

  /**
   * Main update method - controls electrolyzer operation
   * Manages water flow, temperature, and production based on sensor readings
   */
  void update();

  /**
   * Emergency stop method
   * Immediately shuts down all electrolyzer operations
   */
  void emergency_stop();

  /**
   * Reset emergency state to allow operation to resume
   */
  void resetEmergency();

  /**
   * Stop production and reset production state
   * Called when deactivating electrolyzer mode
   */
  void stopProduction();

  /**
   * Get the current electrolyzer state
   * @return Reference to the current state structure
   */
  const State& getState() const { return elecState; }
};

extern ElectrolyzerController electrolyzer_controller;

#endif // ELECTROLYZER_CONTROLLER_H
