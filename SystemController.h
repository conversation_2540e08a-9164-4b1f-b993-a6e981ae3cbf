#ifndef SYSTEM_CONTROLLER_H
#define SYSTEM_CONTROLLER_H

/**
 * SystemController.h
 * Manages the overall system operation mode and transitions between modes
 * Handles mode validation, activation sequences, and safety checks
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * SystemController class
 * Controls the system's operational modes and manages transitions
 * between different modes with appropriate safety checks
 */
class SystemController {
public:
  enum OperationMode : uint8_t {
    MODE_SAFE = 0,
    MODE_ELECTROLYZER,
    MODE_FUEL_CELL,
    MODE_BATTERY_INVERTER,
    MODE_SYSTEM_TEST,
    MODE_EMERGENCY,
    MODE_COUNT
  };

private:
  OperationMode currentMode;
  OperationMode pendingMode;
  bool inTransition;
  unsigned long transitionStart;
  enum SubState { TRANS_IDLE, TRANS_WAIT, TRANS_VERIFY, TRANS_DONE } subState;
  unsigned long stepStartTime;

  bool validate_transition(OperationMode m);
  void begin_transition(OperationMode m);
  void complete_transition();
  void deactivate_current_mode();
  void activateModeStep();
  void doActivateElectrolyzer();
  void doActivateFuelCell();
  void doActivateBatteryInverter();
  void doSystemTest();
  void doEmergency();

public:
  SystemController();

  /**
   * Request a mode change with safety validation
   * @param m The target operation mode
   */
  void request_mode(OperationMode m);

  /**
   * Update method called from main loop
   * Handles mode transitions and state machine
   */
  void update();

  /**
   * Get the current operation mode
   * @return Current system operation mode
   */
  OperationMode get_mode() const;

  /**
   * Force a mode change without validation
   * Used for emergency situations
   * @param m The target operation mode
   */
  void force_mode(OperationMode m);

  /**
   * Get the string name of an operation mode
   * @param mode The operation mode
   * @return String representation of the mode
   */
  const __FlashStringHelper* getModeName(OperationMode mode);
};

extern SystemController system_controller;

#endif // SYSTEM_CONTROLLER_H
